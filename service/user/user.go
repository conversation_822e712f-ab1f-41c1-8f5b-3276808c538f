package user

import (
	"context"
	"sase-indicator-stat/common/client"
	"sase-indicator-stat/common/config"

	"go.mongodb.org/mongo-driver/mongo"
)

type Repository interface {
	GetUserInfo(ctx context.Context, startTime, endTime int64, orgName string) (interface{}, error)
}

type repository struct {
	config     *config.AppConfig
	collection *mongo.Collection
}

func NewUserService() Repository {
	return &repository{
		collection: client.RmSaseMongo.Database.Collection("organization_user"),
	}
}

func (u *repository) GetUserInfo(ctx context.Context, startTime, endTime int64, orgName string) (interface{}, error) {
	return nil, nil
}
