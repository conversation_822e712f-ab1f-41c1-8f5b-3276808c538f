package connector_info

import (
	"context"
	"sase-indicator-stat/common/client"
	"sase-indicator-stat/common/config"

	"go.mongodb.org/mongo-driver/mongo"
)

type Service interface {
}

type ConnectorInfoService struct {
	config     *config.AppConfig
	Collection *mongo.Collection
}

func NewConnectorInfoService() Service {
	return &ConnectorInfoService{
		config:     config.Config(),
		Collection: client.RmSaseMongo.Database.Collection("organization_user"),
	}
}

func (u *ConnectorInfoService) GetUserInfo(ctx context.Context, startTime, endTime int64, orgName string) (interface{}, error) {
	return nil, nil
}
