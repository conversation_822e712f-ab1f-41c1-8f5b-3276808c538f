package client

import (
	"context"
	"fmt"
	"os"

	"sase-indicator-stat/common/config"

	"rm.git/client_api/rm_common_libs.git/v2/common/clients"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"

	rmCommonInterfaces "rm.git/client_api/rm_common_libs.git/v2/interfaces"
)

var (
	conf = config.Config()

	Redis       rmCommonInterfaces.RedisClient
	RmSaseMongo *clients.MongoDB
)

func Init() {
	var err error
	log.Infof("[初始化] - Redis: %+v", conf.Redis)

	if Redis, err = clients.NewRedis(conf.Redis); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}

	log.Infof("[初始化] - MongoDB: %+v", conf.RmSaseMongo)
	if RmSaseMongo, err = clients.NewMongodb(conf.RmSaseMongo, conf.RmSaseMongo.DBName); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}
}

func Close() error {
	_ = Redis.Close()
	_ = RmSaseMongo.Client.Disconnect(context.Background())
	return nil
}
