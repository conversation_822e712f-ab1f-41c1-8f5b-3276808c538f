package config

import (
	"fmt"
	"os"

	"rm.git/client_api/rm_common_libs.git/v2/common/configs"

	"github.com/spf13/viper"
)

var (
	v         = viper.New()
	appConfig = NewAppConfig()
)

type Service struct {
	Name    string `mapstructure:"name"`
	Mode    string `mapstructure:"mode"`
	Version string `mapstructure:"version"`
	Addr    string `mapstructure:"addr"`
}

type TaskSetting struct {
	RunTask      bool   `mapstructure:"run_task"`
	TaskDuration string `mapstructure:"task_duration"`
}

type AppConfig struct {
	Service     Service                `mapstructure:"service"`
	Logging     map[string]interface{} `mapstructure:"logging"`
	Redis       configs.RedisConfig    `mapstructure:"redis_default"`
	RmSaseMongo configs.MongoDBConfig  `mapstructure:"mongodb_rm_sase"`
	TaskSetting TaskSetting            `mapstructure:"task_setting"`
}

func NewAppConfig() *AppConfig {
	return &AppConfig{}
}

func init() {

	v.AutomaticEnv()
	v.SetConfigType("toml")
	v.AddConfigPath("conf")

	// 加载业务配置
	v.SetConfigName("config.toml")

	if err := v.ReadInConfig(); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：load config failed，", err, "\033[0m")
		os.Exit(-1)
	}

	if err := v.Unmarshal(&appConfig); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：load config failed，", err, "\033[0m")
		os.Exit(-1)
	}

}

func Config() *AppConfig {
	return appConfig
}
