package entity

type StatData struct {
	ProductId       string `json:"product_id"`
	Action          string `json:"action"`
	Count           int64  `json:"count"`
	Id              string `json:"id"`
	Appid           string `json:"appid"`
	UserCode        string `json:"user_code"`
	UserReqIp       string `json:"user_req_ip"`
	UserReqLocation string `json:"user_req_location"`
	ClientId        string `json:"client_id"`
	EdgeId          string `json:"edge_id"`
	ClusterId       string `json:"cluster_id"`
	ConnectorId     string `json:"connector_id"`
	Content         string `json:"content"`
	StartTime       string `json:"start_time"`
	EndTime         string `json:"end_time"`
}
