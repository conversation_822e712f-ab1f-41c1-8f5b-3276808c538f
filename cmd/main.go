package main

import (
	"context"
	"errors"
	"net/http"
	"os"
	"os/signal"
	"sase-indicator-stat/common/client"
	"sase-indicator-stat/task"
	"syscall"
	"time"

	"sase-indicator-stat/common/config"
	"sase-indicator-stat/controller"
	"sase-indicator-stat/service"

	"rm.git/client_api/rm_common_libs.git/v2/library/log"

	"github.com/gin-gonic/gin"
)

var conf = config.Config()

type Servers struct {
	ginEngine  *gin.Engine
	httpServer *http.Server
}

func NewServer() *Servers {

	// 初始化service
	service.Init()

	// 创建 Gin 引擎
	gin.SetMode(gin.DebugMode)
	engine := gin.Default()

	// 注册认证路由
	controller.RegisterRoutes(engine)

	// 创建 HTTP 服务器
	httpServer := &http.Server{
		Addr:    conf.Service.Addr,
		Handler: engine,
	}

	return &Servers{
		ginEngine:  engine,
		httpServer: httpServer,
	}
}

func (s *Servers) Init() {

	go func() {
		defer task.DeferRecover()
		task.Init()
		task.Run()
	}()
}

func (s *Servers) Start() {
	// 启动 HTTP 服务器
	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("HTTP server error: %v", err)
		}
	}()

	log.Infof("HTTP server started on %s", s.httpServer.Addr)

	// 设置优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 关闭服务器
	s.Close()
}

func (s *Servers) Close() {
	log.Errorf("[关闭] - 信号： - 收到终止信号\n")

	// 关闭 HTTP 服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := s.httpServer.Shutdown(ctx); err != nil {
		log.Errorf("HTTP server shutdown error: %v", err)
	}

	// 关闭 Redis 连接
	_ = client.Close()

	task.Stop()
}

func main() {

	client.Init()

	log.Infof("[启动] - %s ( %s ) %s\n", conf.Service.Name, conf.Service.Version, conf.Service.Mode)

	server := NewServer()

	server.Init()

	server.Start()
}
