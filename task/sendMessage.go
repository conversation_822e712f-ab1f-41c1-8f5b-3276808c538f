package task

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sase-indicator-stat/domain/entity"
	"time"
)

func SendMessage(data *entity.StatData) {
	// 创建管道（生产者-消费者模型）
	reader, writer := io.Pipe()

	// 启动 Goroutine 动态写入数据
	go func(data *entity.StatData) {

		defer func(writer *io.PipeWriter) {
			_ = writer.Close()
		}(writer)

		for i := 0; i < 10; i++ {
			//data := fmt.Sprintf(`{"aaa":1,"bb":%v}`+"\n", i) // 带换行符
			statData, err := json.Marshal(data)
			if err != nil {
				return
			}

			if _, err := io.WriteString(writer, string(statData)); err != nil {
				return
			}
			fmt.Printf("[客户端] 已发送: %s", data) // 输出发送内容
			time.Sleep(500 * time.Millisecond)      // 控制发送速度
		}
	}(data)

	// 创建流式请求
	req, _ := http.NewRequest("POST", "http://***************:16003/log-tracker/v1/indicator_report", reader)
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		panic(err)
	}

	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 打印服务端响应
	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("服务端响应: %s\n", body)
}
