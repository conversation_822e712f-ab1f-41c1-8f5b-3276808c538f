package task

import (
	"rm.git/client_api/rm_common_libs.git/v2/library/log"

	"sase-indicator-stat/common/config"
)

type ITask interface {
	Name() string
	Run() error
	Stop() error
}

var (
	tasks []ITask
	conf  = config.Config()
)

func Init() {
	if conf.TaskSetting.RunTask {
		log.Info("run task")
	} else {
		log.Info("not run task")
		return
	}

	tasks = make([]ITask, 0)
	tasks = append(tasks, NewIndicatorLibrary())
}

func Run() {
	for _, task := range tasks {
		go func(task ITask) {
			if err := task.Run(); err != nil {
				log.Errorf("task %s run error: %v", task.Name(), err)
			}
		}(task)
	}
}

func Stop() {
	for _, task := range tasks {
		if err := task.Stop(); err != nil {
			log.Errorf("task %s run error: %v", task.Name(), err)
		}
	}
}

func DeferRecover() {
	if r := recover(); r != nil {
		log.<PERSON><PERSON>rf(" recover err %v", r)
	}
}
