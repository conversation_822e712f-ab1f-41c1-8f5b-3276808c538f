package task

import (
	"context"
	"sase-indicator-stat/common/utils"
	"sase-indicator-stat/service/user"
	"time"

	"rm.git/client_api/rm_common_libs.git/v2/library/log"
)

type IndicatorLibrary struct {
	interval int64
	ticker   *time.Ticker
}

func NewIndicatorLibrary() *IndicatorLibrary {
	interval, err := utils.ParseTimeString(conf.TaskSetting.TaskDuration)

	if err != nil {
		log.Fatal(err)
	}

	return &IndicatorLibrary{
		interval: interval,
		ticker:   time.NewTicker(time.Duration(interval) * time.Second),
	}
}

func (i *IndicatorLibrary) Run() error {

	defer DeferRecover()
	//  比上传的任务要慢，需要后面执行

	i.runTask()
	for range i.ticker.C {
		i.runTask()
	}

	return nil
}

func (i *IndicatorLibrary) runTask() {
	ctx := context.Background()

	var startTime, endTime int64

	info, err := user.Repository.GetUserInfo(ctx, startTime, endTime, "")

}

func (i *IndicatorLibrary) Stop() error {
	i.ticker.Stop()
	return nil
}

func (i *IndicatorLibrary) Name() string {
	return "indicator-stat"
}
