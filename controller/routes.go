package controller

import (
	"github.com/gin-gonic/gin"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"
)

func RegisterRoutes(r *gin.Engine) {
	log.Infof("RegisterRoutes: start")
	r.Use(gin.Recovery())
	// 创建认证中间件

	log.Infof("RegisterRoutes: end")

	v1 := r.Group("/sase/health")
	{
		v1.GET("/ping", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "pong",
			})
		})
	}

	log.Infof("RegisterRoutes: end")

}
